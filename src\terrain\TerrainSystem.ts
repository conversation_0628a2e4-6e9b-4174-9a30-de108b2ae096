import * as THREE from 'three';
import { LODSelector, LODSelectionOptions } from './lod/LODSelector';
import { DataPipelineCoordinator, PipelineProgress } from '../data/pipeline/DataPipelineCoordinator';
import { QuadtreeNode, NodeState } from './lod/QuadtreeNode';
import { QuantizedMeshData } from '../types/TerrainTypes';

export class TerrainSystem {
    private scene: THREE.Scene;
    private camera: THREE.PerspectiveCamera;
    private activeTiles: Map<string, THREE.Mesh> = new Map();
    private memoryUsage = 0;

    // LOD and data systems
    private lodSelector: LODSelector;
    private dataPipeline: DataPipelineCoordinator;
    private meshCache = new Map<string, QuantizedMeshData>();

    // Earth parameters
    private readonly EARTH_RADIUS = 6371000; // meters
    private readonly MAX_ZOOM_LEVEL = 14;

    // Configuration
    private lodOptions: LODSelectionOptions = {
        errorThreshold: 2.0,
        maxConcurrentLoads: 10,
        enableAdaptiveThreshold: true,
        enableViewDependentScaling: true,
        enableTemporalCoherence: true,
        enableHorizonCulling: true,
        prefetchRadius: 2,
        memoryBudgetMB: 256
    };

    // Performance tracking
    private isInitialized = false;
    private lastUpdateTime = 0;
    private frameCount = 0;

    constructor(scene: THREE.Scene, camera: THREE.PerspectiveCamera) {
        this.scene = scene;
        this.camera = camera;

        this.lodSelector = new LODSelector();
        this.dataPipeline = new DataPipelineCoordinator(this.onPipelineProgress.bind(this));

        this.initializeSystem();
    }

    private async initializeSystem(): Promise<void> {
        console.log('Initializing DreaMatch Terrain System...');

        // Start with basic sphere for immediate feedback
        this.initializeBasicSphere();

        try {
            // Generate sample dataset for testing
            console.log('Generating sample terrain dataset...');
            const dataset = await this.dataPipeline.generateSampleDataset();

            // Cache the mesh data
            for (const [tileKey, meshData] of dataset.tiles) {
                this.meshCache.set(tileKey, meshData);
            }

            console.log(`Terrain system initialized with ${dataset.tiles.size} tiles`);
            this.isInitialized = true;

            // Remove basic sphere once real data is ready
            this.removeBasicSphere();

        } catch (error) {
            console.error('Failed to initialize terrain system:', error);
            // Keep basic sphere as fallback
        }
    }

    private initializeBasicSphere(): void {
        // Create a basic Earth sphere as placeholder
        const geometry = new THREE.SphereGeometry(this.EARTH_RADIUS, 64, 32);

        // Create a simple material with basic Earth colors
        const material = new THREE.MeshLambertMaterial({
            color: 0x4a90e2,
            wireframe: false
        });

        const earthMesh = new THREE.Mesh(geometry, material);
        earthMesh.name = 'earth-base';
        this.scene.add(earthMesh);

        this.activeTiles.set('earth-base', earthMesh);

        console.log('Basic Earth sphere initialized');
    }

    private removeBasicSphere(): void {
        const basicSphere = this.activeTiles.get('earth-base');
        if (basicSphere) {
            this.scene.remove(basicSphere);
            if (basicSphere.geometry) basicSphere.geometry.dispose();
            if (basicSphere.material) {
                if (Array.isArray(basicSphere.material)) {
                    basicSphere.material.forEach(mat => mat.dispose());
                } else {
                    basicSphere.material.dispose();
                }
            }
            this.activeTiles.delete('earth-base');
        }
    }

    public update(camera: THREE.PerspectiveCamera): void {
        this.frameCount++;
        const currentTime = performance.now();

        if (!this.isInitialized) {
            return; // Wait for initialization
        }

        // Get viewport dimensions
        const canvas = document.getElementById('canvas') as HTMLCanvasElement;
        const viewportWidth = canvas ? canvas.width : window.innerWidth;
        const viewportHeight = canvas ? canvas.height : window.innerHeight;

        // Run LOD selection
        const lodResult = this.lodSelector.selectLOD(
            camera,
            viewportWidth,
            viewportHeight,
            this.lodOptions
        );

        // Update terrain meshes based on LOD selection
        this.updateTerrainMeshes(lodResult.selectedNodes);

        // Process loading queue
        this.processLoadingNodes(lodResult.loadingNodes);

        // Update memory usage
        this.memoryUsage = lodResult.stats.memoryUsageMB;

        this.lastUpdateTime = currentTime;
    }

    private updateTerrainMeshes(selectedNodes: QuadtreeNode[]): void {
        // Remove meshes that are no longer selected
        const currentTileKeys = new Set(selectedNodes.map(node => node.getKey()));

        for (const [tileKey, mesh] of this.activeTiles) {
            if (tileKey !== 'earth-base' && !currentTileKeys.has(tileKey)) {
                this.scene.remove(mesh);
                if (mesh.geometry) mesh.geometry.dispose();
                if (mesh.material) {
                    if (Array.isArray(mesh.material)) {
                        mesh.material.forEach(mat => mat.dispose());
                    } else {
                        mesh.material.dispose();
                    }
                }
                this.activeTiles.delete(tileKey);
            }
        }

        // Add or update meshes for selected nodes
        for (const node of selectedNodes) {
            if (node.state === NodeState.LOADED) {
                this.ensureNodeMesh(node);
            }
        }
    }

    private ensureNodeMesh(node: QuadtreeNode): void {
        const tileKey = node.getKey();

        if (this.activeTiles.has(tileKey)) {
            return; // Already have mesh for this tile
        }

        const meshData = this.meshCache.get(tileKey);
        if (!meshData) {
            return; // No mesh data available
        }

        // Create Three.js mesh from quantized mesh data
        const mesh = this.createMeshFromQuantizedData(meshData, node);
        if (mesh) {
            mesh.name = `terrain-${tileKey}`;
            this.scene.add(mesh);
            this.activeTiles.set(tileKey, mesh);
            node.mesh = mesh;
        }
    }

    private createMeshFromQuantizedData(meshData: QuantizedMeshData, node: QuadtreeNode): THREE.Mesh | null {
        try {
            // Create geometry from quantized mesh data
            const geometry = new THREE.BufferGeometry();

            // Dequantize vertices
            const vertices = this.dequantizeVertices(meshData, node.bounds);
            const indices = Array.from(meshData.indexData.indices);

            geometry.setIndex(indices);
            geometry.setAttribute('position', new THREE.Float32BufferAttribute(vertices, 3));
            geometry.computeVertexNormals();

            // Create material
            const material = new THREE.MeshLambertMaterial({
                color: 0x8B7355,
                wireframe: false
            });

            return new THREE.Mesh(geometry, material);

        } catch (error) {
            console.error(`Failed to create mesh for tile ${node.getKey()}:`, error);
            return null;
        }
    }

    private dequantizeVertices(meshData: QuantizedMeshData, bounds: any): Float32Array {
        const { u, v, height } = meshData.vertexData;
        const vertexCount = u.length;
        const vertices = new Float32Array(vertexCount * 3);

        const { minimumHeight, maximumHeight } = meshData.header;

        for (let i = 0; i < vertexCount; i++) {
            // Dequantize coordinates
            const longitude = bounds.west + (u[i] / 32767) * (bounds.east - bounds.west);
            const latitude = bounds.south + (v[i] / 32767) * (bounds.north - bounds.south);
            const elevation = minimumHeight + (height[i] / 32767) * (maximumHeight - minimumHeight);

            // Convert to Cartesian coordinates
            const cartesian = this.geodeticToCartesian(longitude, latitude, elevation);

            vertices[i * 3] = cartesian.x;
            vertices[i * 3 + 1] = cartesian.y;
            vertices[i * 3 + 2] = cartesian.z;
        }

        return vertices;
    }

    private geodeticToCartesian(longitude: number, latitude: number, height: number): { x: number, y: number, z: number } {
        const lonRad = longitude * Math.PI / 180;
        const latRad = latitude * Math.PI / 180;
        const radius = this.EARTH_RADIUS + height;

        return {
            x: radius * Math.cos(latRad) * Math.cos(lonRad),
            y: radius * Math.cos(latRad) * Math.sin(lonRad),
            z: radius * Math.sin(latRad)
        };
    }

    private processLoadingNodes(loadingNodes: QuadtreeNode[]): void {
        // Process nodes that need to be loaded
        for (const node of loadingNodes) {
            if (node.state === NodeState.LOADING) {
                this.loadNodeData(node);
            }
        }
    }

    private async loadNodeData(node: QuadtreeNode): Promise<void> {
        const tileKey = node.getKey();

        try {
            // Check if we already have the data cached
            if (this.meshCache.has(tileKey)) {
                this.lodSelector.markNodeLoaded(node);
                return;
            }

            // Load tile data from pipeline
            const meshData = await this.dataPipeline.getTile(node.coordinate);

            if (meshData) {
                this.meshCache.set(tileKey, meshData);
                this.lodSelector.markNodeLoaded(node);
            } else {
                this.lodSelector.markNodeFailed(node);
            }

        } catch (error) {
            console.error(`Failed to load tile ${tileKey}:`, error);
            this.lodSelector.markNodeFailed(node);
        }
    }

    private onPipelineProgress(progress: PipelineProgress): void {
        // Handle pipeline progress updates
        console.log(`Pipeline progress: ${progress.stage} ${progress.progress}% (${progress.tilesCompleted}/${progress.totalTiles})`);

        if (progress.errors.length > 0) {
            console.warn('Pipeline errors:', progress.errors);
        }
    }

    public getActiveTileCount(): number {
        return this.activeTiles.size;
    }

    public getMemoryUsage(): number {
        return Math.round(this.memoryUsage);
    }

    public getLODStats(): any {
        if (!this.isInitialized) {
            return { status: 'initializing' };
        }

        return {
            selectedNodes: this.lodSelector.getSelectedNodes().length,
            loadingNodes: this.lodSelector.getLoadingNodes().length,
            cachedMeshes: this.meshCache.size,
            frameCount: this.frameCount
        };
    }

    public dispose(): void {
        // Clean up resources
        this.activeTiles.forEach((mesh) => {
            this.scene.remove(mesh);
            if (mesh.geometry) mesh.geometry.dispose();
            if (mesh.material) {
                if (Array.isArray(mesh.material)) {
                    mesh.material.forEach(mat => mat.dispose());
                } else {
                    mesh.material.dispose();
                }
            }
        });
        this.activeTiles.clear();
        this.meshCache.clear();
        this.lodSelector.reset();
        this.dataPipeline.clearCache();
    }
}
