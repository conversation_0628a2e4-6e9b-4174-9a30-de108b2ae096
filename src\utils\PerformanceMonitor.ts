export class PerformanceMonitor {
    private frameCount = 0;
    private lastTime = performance.now();
    private fps = 0;
    private frameTimeHistory: number[] = [];
    private maxHistoryLength = 60; // Keep 1 second of frame times at 60fps

    constructor() {
        this.lastTime = performance.now();
    }

    public update(): void {
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        
        this.frameTimeHistory.push(deltaTime);
        if (this.frameTimeHistory.length > this.maxHistoryLength) {
            this.frameTimeHistory.shift();
        }

        this.frameCount++;
        
        // Update FPS every 10 frames for smoother display
        if (this.frameCount % 10 === 0) {
            const averageFrameTime = this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;
            this.fps = Math.round(1000 / averageFrameTime);
        }

        this.lastTime = currentTime;
    }

    public getFPS(): number {
        return this.fps;
    }

    public getAverageFrameTime(): number {
        if (this.frameTimeHistory.length === 0) return 0;
        return this.frameTimeHistory.reduce((a, b) => a + b, 0) / this.frameTimeHistory.length;
    }

    public getFrameTimeVariance(): number {
        if (this.frameTimeHistory.length < 2) return 0;
        
        const average = this.getAverageFrameTime();
        const variance = this.frameTimeHistory.reduce((sum, time) => {
            return sum + Math.pow(time - average, 2);
        }, 0) / this.frameTimeHistory.length;
        
        return Math.sqrt(variance);
    }
}
