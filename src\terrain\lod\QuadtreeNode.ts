import * as THREE from 'three';
import { TileCoordinate, GeographicBounds, TerrainTile } from '../../types/TerrainTypes';
import { TilePyramidGenerator } from '../../data/TilePyramidGenerator';

export enum NodeState {
    UNLOADED = 'unloaded',
    LOADING = 'loading',
    LOADED = 'loaded',
    FAILED = 'failed'
}

export class QuadtreeNode {
    public readonly coordinate: TileCoordinate;
    public readonly bounds: GeographicBounds;
    public readonly geometricError: number;
    public readonly level: number;
    
    public state: NodeState = NodeState.UNLOADED;
    public children: QuadtreeNode[] = [];
    public parent: QuadtreeNode | null = null;
    public tile: TerrainTile | null = null;
    public mesh: THREE.Mesh | null = null;
    
    // Culling and selection
    public isVisible = false;
    public isSelected = false;
    public lastFrameSelected = -1;
    public distanceToCamera = Infinity;
    public screenSpaceError = 0;
    
    // Bounding volumes for culling
    public boundingSphere: THREE.Sphere;
    public boundingBox: THREE.Box3;

    constructor(coordinate: TileCoordinate, parent: QuadtreeNode | null = null) {
        this.coordinate = coordinate;
        this.level = coordinate.z;
        this.parent = parent;
        this.bounds = TilePyramidGenerator.getTileBounds(coordinate);
        this.geometricError = TilePyramidGenerator.calculateGeometricError(coordinate.z);
        
        this.initializeBoundingVolumes();
    }

    /**
     * Initialize bounding volumes for frustum culling
     */
    private initializeBoundingVolumes(): void {
        // Calculate bounding box in world coordinates
        const earthRadius = 6371000;
        
        // Convert geographic bounds to Cartesian coordinates
        const corners = [
            this.geodeticToCartesian(this.bounds.west, this.bounds.north, 0),
            this.geodeticToCartesian(this.bounds.east, this.bounds.north, 0),
            this.geodeticToCartesian(this.bounds.west, this.bounds.south, 0),
            this.geodeticToCartesian(this.bounds.east, this.bounds.south, 0),
            this.geodeticToCartesian(this.bounds.west, this.bounds.north, 10000), // Add height
            this.geodeticToCartesian(this.bounds.east, this.bounds.north, 10000),
            this.geodeticToCartesian(this.bounds.west, this.bounds.south, 10000),
            this.geodeticToCartesian(this.bounds.east, this.bounds.south, 10000)
        ];

        this.boundingBox = new THREE.Box3();
        corners.forEach(corner => {
            this.boundingBox.expandByPoint(new THREE.Vector3(corner.x, corner.y, corner.z));
        });

        // Create bounding sphere
        const center = new THREE.Vector3();
        this.boundingBox.getCenter(center);
        const size = new THREE.Vector3();
        this.boundingBox.getSize(size);
        const radius = size.length() / 2;
        
        this.boundingSphere = new THREE.Sphere(center, radius);
    }

    /**
     * Create child nodes if they don't exist
     */
    public subdivide(): void {
        if (this.children.length > 0) {
            return; // Already subdivided
        }

        const childCoordinates = TilePyramidGenerator.getChildTiles(this.coordinate);
        
        for (const childCoord of childCoordinates) {
            const childNode = new QuadtreeNode(childCoord, this);
            this.children.push(childNode);
        }
    }

    /**
     * Check if this node can be subdivided
     */
    public canSubdivide(): boolean {
        return this.level < 14; // Max zoom level
    }

    /**
     * Check if this node should be subdivided based on error threshold
     */
    public shouldSubdivide(errorThreshold: number): boolean {
        return this.canSubdivide() && this.screenSpaceError > errorThreshold;
    }

    /**
     * Get all leaf nodes (nodes without children or with unloaded children)
     */
    public getLeafNodes(): QuadtreeNode[] {
        if (this.children.length === 0) {
            return [this];
        }

        const leaves: QuadtreeNode[] = [];
        for (const child of this.children) {
            if (child.state === NodeState.LOADED) {
                leaves.push(...child.getLeafNodes());
            } else {
                // If child is not loaded, this node acts as a leaf
                leaves.push(this);
                break;
            }
        }

        return leaves;
    }

    /**
     * Get all nodes at a specific level in the subtree
     */
    public getNodesAtLevel(targetLevel: number): QuadtreeNode[] {
        if (this.level === targetLevel) {
            return [this];
        }

        if (this.level > targetLevel || this.children.length === 0) {
            return [];
        }

        const nodes: QuadtreeNode[] = [];
        for (const child of this.children) {
            nodes.push(...child.getNodesAtLevel(targetLevel));
        }

        return nodes;
    }

    /**
     * Traverse the quadtree and call a function on each node
     */
    public traverse(callback: (node: QuadtreeNode) => void): void {
        callback(this);
        
        for (const child of this.children) {
            child.traverse(callback);
        }
    }

    /**
     * Find the best node to render at a given position
     */
    public findBestNodeForPosition(longitude: number, latitude: number): QuadtreeNode {
        // Check if position is within this node's bounds
        if (!this.containsPosition(longitude, latitude)) {
            return this;
        }

        // If no children or children not loaded, return this node
        if (this.children.length === 0) {
            return this;
        }

        // Find the child that contains the position
        for (const child of this.children) {
            if (child.containsPosition(longitude, latitude)) {
                return child.findBestNodeForPosition(longitude, latitude);
            }
        }

        return this;
    }

    /**
     * Check if a geographic position is within this node's bounds
     */
    public containsPosition(longitude: number, latitude: number): boolean {
        return longitude >= this.bounds.west &&
               longitude <= this.bounds.east &&
               latitude >= this.bounds.south &&
               latitude <= this.bounds.north;
    }

    /**
     * Calculate distance from camera to this node
     */
    public updateDistanceToCamera(cameraPosition: THREE.Vector3): void {
        this.distanceToCamera = this.boundingSphere.distanceToPoint(cameraPosition);
    }

    /**
     * Check if this node intersects with a frustum
     */
    public intersectsFrustum(frustum: THREE.Frustum): boolean {
        return frustum.intersectsSphere(this.boundingSphere);
    }

    /**
     * Get the key string for this node
     */
    public getKey(): string {
        return TilePyramidGenerator.tileToKey(this.coordinate);
    }

    /**
     * Clean up resources
     */
    public dispose(): void {
        if (this.mesh) {
            if (this.mesh.geometry) this.mesh.geometry.dispose();
            if (this.mesh.material) {
                if (Array.isArray(this.mesh.material)) {
                    this.mesh.material.forEach(mat => mat.dispose());
                } else {
                    this.mesh.material.dispose();
                }
            }
            this.mesh = null;
        }

        this.tile = null;
        this.state = NodeState.UNLOADED;

        // Dispose children
        for (const child of this.children) {
            child.dispose();
        }
        this.children = [];
    }

    /**
     * Convert geodetic coordinates to Cartesian
     */
    private geodeticToCartesian(longitude: number, latitude: number, height: number): { x: number, y: number, z: number } {
        const earthRadius = 6371000;
        const lonRad = longitude * Math.PI / 180;
        const latRad = latitude * Math.PI / 180;
        const radius = earthRadius + height;

        return {
            x: radius * Math.cos(latRad) * Math.cos(lonRad),
            y: radius * Math.cos(latRad) * Math.sin(lonRad),
            z: radius * Math.sin(latRad)
        };
    }
}
