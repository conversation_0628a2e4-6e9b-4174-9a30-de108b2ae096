import { DEMFetcher } from '../DEMFetcher';
import { TilePyramidGenerator } from '../TilePyramidGenerator';
import { QuantizedMeshConverter } from '../QuantizedMeshConverter';
import { LayerMetadataProducer } from '../LayerMetadataProducer';
import { TileCoordinate, ElevationData, QuantizedMeshData, LayerMetadata } from '../../types/TerrainTypes';

export interface PipelineProgress {
    stage: 'fetching' | 'processing' | 'converting' | 'complete';
    progress: number; // 0-100
    currentTile?: string;
    tilesCompleted: number;
    totalTiles: number;
    errors: string[];
}

export class DataPipelineCoordinator {
    private demFetcher: DEMFetcher;
    private tileCache = new Map<string, QuantizedMeshData>();
    private progressCallback?: (progress: PipelineProgress) => void;

    constructor(progressCallback?: (progress: PipelineProgress) => void) {
        this.demFetcher = new DEMFetcher();
        this.progressCallback = progressCallback;
    }

    /**
     * Generate the complete terrain dataset for specified zoom levels
     */
    public async generateTerrainDataset(
        minZoom: number = 0,
        maxZoom: number = 6, // Start with lower zoom for testing
        sampleRegion?: { west: number; east: number; north: number; south: number }
    ): Promise<{
        metadata: LayerMetadata;
        tiles: Map<string, QuantizedMeshData>;
    }> {
        console.log(`Starting terrain dataset generation (Z${minZoom}-Z${maxZoom})`);
        
        const progress: PipelineProgress = {
            stage: 'fetching',
            progress: 0,
            tilesCompleted: 0,
            totalTiles: 0,
            errors: []
        };

        try {
            // Calculate total tiles to process
            let totalTiles = 0;
            for (let z = minZoom; z <= maxZoom; z++) {
                const tilesAtLevel = TilePyramidGenerator.getTilesAtZoomLevel(z);
                totalTiles += sampleRegion ? 
                    this.filterTilesByRegion(tilesAtLevel, sampleRegion).length : 
                    tilesAtLevel.length;
            }
            
            progress.totalTiles = totalTiles;
            this.reportProgress(progress);

            // Process each zoom level
            for (let z = minZoom; z <= maxZoom; z++) {
                await this.processZoomLevel(z, progress, sampleRegion);
            }

            // Generate metadata
            progress.stage = 'complete';
            progress.progress = 100;
            this.reportProgress(progress);

            const metadata = LayerMetadataProducer.generateLayerMetadata();
            
            console.log(`Dataset generation complete: ${this.tileCache.size} tiles generated`);
            
            return {
                metadata,
                tiles: new Map(this.tileCache)
            };

        } catch (error) {
            progress.errors.push(`Pipeline error: ${error}`);
            this.reportProgress(progress);
            throw error;
        }
    }

    /**
     * Process a single zoom level
     */
    private async processZoomLevel(
        zoomLevel: number,
        progress: PipelineProgress,
        sampleRegion?: { west: number; east: number; north: number; south: number }
    ): Promise<void> {
        console.log(`Processing zoom level ${zoomLevel}`);
        
        const tiles = TilePyramidGenerator.getTilesAtZoomLevel(zoomLevel);
        const tilesToProcess = sampleRegion ? 
            this.filterTilesByRegion(tiles, sampleRegion) : 
            tiles;

        for (const tile of tilesToProcess) {
            try {
                await this.processTile(tile, progress);
            } catch (error) {
                const errorMsg = `Failed to process tile ${TilePyramidGenerator.tileToKey(tile)}: ${error}`;
                progress.errors.push(errorMsg);
                console.error(errorMsg);
            }
        }
    }

    /**
     * Process a single tile
     */
    private async processTile(tile: TileCoordinate, progress: PipelineProgress): Promise<void> {
        const tileKey = TilePyramidGenerator.tileToKey(tile);
        progress.currentTile = tileKey;
        
        // Skip if already processed
        if (this.tileCache.has(tileKey)) {
            progress.tilesCompleted++;
            this.updateProgress(progress);
            return;
        }

        try {
            // Get tile bounds
            const bounds = TilePyramidGenerator.getTileBounds(tile);
            
            // Fetch elevation data
            progress.stage = 'fetching';
            this.reportProgress(progress);
            
            const elevationData = await this.demFetcher.fetchTileElevation(bounds);
            
            // Convert to quantized mesh
            progress.stage = 'converting';
            this.reportProgress(progress);
            
            const quantizedMesh = QuantizedMeshConverter.convertToQuantizedMesh(elevationData);
            
            // Cache the result
            this.tileCache.set(tileKey, quantizedMesh);
            
            progress.tilesCompleted++;
            this.updateProgress(progress);
            
            console.log(`Processed tile ${tileKey} (${progress.tilesCompleted}/${progress.totalTiles})`);
            
        } catch (error) {
            throw new Error(`Tile processing failed: ${error}`);
        }
    }

    /**
     * Filter tiles by geographic region
     */
    private filterTilesByRegion(
        tiles: TileCoordinate[],
        region: { west: number; east: number; north: number; south: number }
    ): TileCoordinate[] {
        return tiles.filter(tile => {
            const bounds = TilePyramidGenerator.getTileBounds(tile);
            
            // Check if tile intersects with region
            return !(
                bounds.east < region.west ||
                bounds.west > region.east ||
                bounds.north < region.south ||
                bounds.south > region.north
            );
        });
    }

    /**
     * Generate a sample dataset for testing (small region)
     */
    public async generateSampleDataset(): Promise<{
        metadata: LayerMetadata;
        tiles: Map<string, QuantizedMeshData>;
    }> {
        // Sample region: San Francisco Bay Area
        const sampleRegion = {
            west: -123,
            east: -121,
            north: 38,
            south: 37
        };

        console.log('Generating sample dataset for San Francisco Bay Area');
        
        return this.generateTerrainDataset(0, 4, sampleRegion);
    }

    /**
     * Get a specific tile from cache or generate it
     */
    public async getTile(tile: TileCoordinate): Promise<QuantizedMeshData | null> {
        const tileKey = TilePyramidGenerator.tileToKey(tile);
        
        if (this.tileCache.has(tileKey)) {
            return this.tileCache.get(tileKey)!;
        }

        try {
            const progress: PipelineProgress = {
                stage: 'fetching',
                progress: 0,
                tilesCompleted: 0,
                totalTiles: 1,
                errors: []
            };

            await this.processTile(tile, progress);
            return this.tileCache.get(tileKey) || null;
        } catch (error) {
            console.error(`Failed to generate tile ${tileKey}:`, error);
            return null;
        }
    }

    /**
     * Clear cache and reset
     */
    public clearCache(): void {
        this.tileCache.clear();
        this.demFetcher.clearCache();
    }

    /**
     * Get cache statistics
     */
    public getCacheStats(): {
        tileCount: number;
        demCacheSize: number;
        estimatedMemoryMB: number;
    } {
        const avgTileSizeKB = 50; // Estimated
        const estimatedMemoryMB = (this.tileCache.size * avgTileSizeKB) / 1024;

        return {
            tileCount: this.tileCache.size,
            demCacheSize: this.demFetcher.getCacheSize(),
            estimatedMemoryMB: Math.round(estimatedMemoryMB * 100) / 100
        };
    }

    private updateProgress(progress: PipelineProgress): void {
        progress.progress = Math.round((progress.tilesCompleted / progress.totalTiles) * 100);
        this.reportProgress(progress);
    }

    private reportProgress(progress: PipelineProgress): void {
        if (this.progressCallback) {
            this.progressCallback({ ...progress });
        }
    }
}
