import { ElevationData, GeographicBounds, OpenMeteoElevationRequest, OpenMeteoElevationResponse } from '../types/TerrainTypes';

export class DEMFetcher {
    private static readonly OPEN_METEO_BASE_URL = 'https://api.open-meteo.com/v1/elevation';
    private static readonly MAX_POINTS_PER_REQUEST = 1000; // Open-Meteo limit
    private static readonly COPERNICUS_RESOLUTION = 90; // meters
    
    private cache = new Map<string, ElevationData>();

    /**
     * Fetch elevation data for a 1°×1° tile
     */
    public async fetchTileElevation(bounds: GeographicBounds, resolution: number = 90): Promise<ElevationData> {
        const cacheKey = this.getCacheKey(bounds, resolution);
        
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey)!;
        }

        console.log(`Fetching elevation data for bounds: ${JSON.stringify(bounds)}`);

        const elevationData = await this.fetchElevationGrid(bounds, resolution);
        this.cache.set(cacheKey, elevationData);
        
        return elevationData;
    }

    /**
     * Create a grid of lat/lon points and fetch elevation data
     */
    private async fetchElevationGrid(bounds: GeographicBounds, resolution: number): Promise<ElevationData> {
        // Calculate grid dimensions based on resolution
        const earthCircumference = 40075000; // meters at equator
        const degreesPerMeter = 360 / earthCircumference;
        const gridSpacing = resolution * degreesPerMeter;

        const width = Math.ceil((bounds.east - bounds.west) / gridSpacing);
        const height = Math.ceil((bounds.north - bounds.south) / gridSpacing);

        console.log(`Creating ${width}x${height} elevation grid`);

        // Generate coordinate arrays
        const coordinates = this.generateCoordinateGrid(bounds, width, height);
        
        // Fetch elevation data in batches
        const elevations = await this.fetchElevationBatches(coordinates);

        return {
            width,
            height,
            bounds,
            elevations: new Float32Array(elevations),
            resolution
        };
    }

    /**
     * Generate a grid of lat/lon coordinates
     */
    private generateCoordinateGrid(bounds: GeographicBounds, width: number, height: number): { lat: number[], lon: number[] } {
        const latitudes: number[] = [];
        const longitudes: number[] = [];

        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const lon = bounds.west + (x / (width - 1)) * (bounds.east - bounds.west);
                const lat = bounds.south + (y / (height - 1)) * (bounds.north - bounds.south);
                
                longitudes.push(lon);
                latitudes.push(lat);
            }
        }

        return { lat: latitudes, lon: longitudes };
    }

    /**
     * Fetch elevation data in batches to respect API limits
     */
    private async fetchElevationBatches(coordinates: { lat: number[], lon: number[] }): Promise<number[]> {
        const totalPoints = coordinates.lat.length;
        const elevations: number[] = new Array(totalPoints);
        
        for (let i = 0; i < totalPoints; i += DEMFetcher.MAX_POINTS_PER_REQUEST) {
            const endIndex = Math.min(i + DEMFetcher.MAX_POINTS_PER_REQUEST, totalPoints);
            
            const batchLat = coordinates.lat.slice(i, endIndex);
            const batchLon = coordinates.lon.slice(i, endIndex);
            
            console.log(`Fetching elevation batch ${Math.floor(i / DEMFetcher.MAX_POINTS_PER_REQUEST) + 1}/${Math.ceil(totalPoints / DEMFetcher.MAX_POINTS_PER_REQUEST)}`);
            
            const batchElevations = await this.fetchElevationBatch(batchLat, batchLon);
            
            for (let j = 0; j < batchElevations.length; j++) {
                elevations[i + j] = batchElevations[j];
            }

            // Add small delay to be respectful to the API
            await this.delay(100);
        }

        return elevations;
    }

    /**
     * Fetch a single batch of elevation data from Open-Meteo
     */
    private async fetchElevationBatch(latitudes: number[], longitudes: number[]): Promise<number[]> {
        const request: OpenMeteoElevationRequest = {
            latitude: latitudes,
            longitude: longitudes,
            format: 'json'
        };

        const url = new URL(DEMFetcher.OPEN_METEO_BASE_URL);
        url.searchParams.append('latitude', latitudes.join(','));
        url.searchParams.append('longitude', longitudes.join(','));
        url.searchParams.append('format', 'json');

        try {
            const response = await fetch(url.toString());
            
            if (!response.ok) {
                throw new Error(`Open-Meteo API error: ${response.status} ${response.statusText}`);
            }

            const data: OpenMeteoElevationResponse = await response.json();
            
            if (!data.elevation || data.elevation.length !== latitudes.length) {
                throw new Error('Invalid elevation data received from Open-Meteo');
            }

            return data.elevation;
        } catch (error) {
            console.error('Error fetching elevation data:', error);
            // Return zero elevations as fallback
            return new Array(latitudes.length).fill(0);
        }
    }

    /**
     * Generate standard 1°×1° tile bounds for a given tile coordinate
     */
    public static getTileBounds(tileX: number, tileY: number): GeographicBounds {
        return {
            west: tileX - 180,
            east: tileX + 1 - 180,
            south: tileY - 90,
            north: tileY + 1 - 90
        };
    }

    /**
     * Get all 1°×1° tiles needed to cover the globe
     */
    public static getGlobalTileCoordinates(): { x: number, y: number }[] {
        const tiles: { x: number, y: number }[] = [];
        
        // 360 tiles in longitude (0-359), 180 tiles in latitude (0-179)
        for (let x = 0; x < 360; x++) {
            for (let y = 0; y < 180; y++) {
                tiles.push({ x, y });
            }
        }
        
        return tiles;
    }

    private getCacheKey(bounds: GeographicBounds, resolution: number): string {
        return `${bounds.west},${bounds.south},${bounds.east},${bounds.north},${resolution}`;
    }

    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    public clearCache(): void {
        this.cache.clear();
    }

    public getCacheSize(): number {
        return this.cache.size;
    }
}
