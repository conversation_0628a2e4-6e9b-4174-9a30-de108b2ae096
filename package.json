{"name": "dreamatch-terrain", "version": "0.0.4", "description": "Multiresolution tile pyramid terrain system with Three.js and WebGPU", "main": "dist/index.js", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "serve": "vite preview --port 3000"}, "keywords": ["terrain", "three.js", "webgpu", "lod", "tiles", "elevation"], "author": "", "license": "ISC", "dependencies": {"@types/three": "^0.178.1", "@webgpu/types": "^0.1.64", "three": "^0.178.0", "typescript": "^5.8.3", "vite": "^7.0.5"}, "devDependencies": {"@types/node": "^24.0.14"}}