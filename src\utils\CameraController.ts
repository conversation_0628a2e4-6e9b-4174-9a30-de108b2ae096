import * as THREE from 'three';

export class CameraController {
    private camera: THREE.PerspectiveCamera;
    private canvas: HTMLCanvasElement;
    private keys: { [key: string]: boolean } = {};
    private mouse = new THREE.Vector2();
    private isMouseDown = false;
    private spherical = new THREE.Spherical();
    private target = new THREE.Vector3(0, 0, 0);
    private lastMousePosition = new THREE.Vector2();

    // Movement parameters
    private moveSpeed = 1000; // meters per second
    private rotateSpeed = 0.005;
    private zoomSpeed = 0.1;
    private minDistance = 6371000 + 1000; // Earth radius + 1km
    private maxDistance = 6371000 * 10; // 10 Earth radii

    constructor(camera: THREE.PerspectiveCamera, canvas: HTMLCanvasElement) {
        this.camera = camera;
        this.canvas = canvas;
        this.setupMouseControls();
        this.updateSpherical();
    }

    private setupMouseControls(): void {
        this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
        this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
        this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
        this.canvas.addEventListener('wheel', this.onWheel.bind(this));
        this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    private onMouseDown(event: MouseEvent): void {
        this.isMouseDown = true;
        this.lastMousePosition.set(event.clientX, event.clientY);
    }

    private onMouseMove(event: MouseEvent): void {
        if (!this.isMouseDown) return;

        const deltaX = event.clientX - this.lastMousePosition.x;
        const deltaY = event.clientY - this.lastMousePosition.y;

        this.spherical.theta -= deltaX * this.rotateSpeed;
        this.spherical.phi += deltaY * this.rotateSpeed;

        // Clamp phi to prevent flipping
        this.spherical.phi = Math.max(0.1, Math.min(Math.PI - 0.1, this.spherical.phi));

        this.lastMousePosition.set(event.clientX, event.clientY);
        this.updateCameraPosition();
    }

    private onMouseUp(): void {
        this.isMouseDown = false;
    }

    private onWheel(event: WheelEvent): void {
        event.preventDefault();
        
        const zoomDelta = event.deltaY * this.zoomSpeed;
        this.spherical.radius *= (1 + zoomDelta * 0.01);
        
        // Clamp zoom distance
        this.spherical.radius = Math.max(this.minDistance, Math.min(this.maxDistance, this.spherical.radius));
        
        this.updateCameraPosition();
    }

    public onKeyDown(event: KeyboardEvent): void {
        this.keys[event.code] = true;
    }

    public onKeyUp(event: KeyboardEvent): void {
        this.keys[event.code] = false;
    }

    public update(): void {
        this.handleKeyboardMovement();
    }

    private handleKeyboardMovement(): void {
        const moveVector = new THREE.Vector3();
        
        if (this.keys['KeyW']) moveVector.z -= 1;
        if (this.keys['KeyS']) moveVector.z += 1;
        if (this.keys['KeyA']) moveVector.x -= 1;
        if (this.keys['KeyD']) moveVector.x += 1;
        if (this.keys['KeyQ']) moveVector.y -= 1;
        if (this.keys['KeyE']) moveVector.y += 1;

        if (moveVector.length() > 0) {
            moveVector.normalize();
            
            // Transform movement relative to camera orientation
            const cameraDirection = new THREE.Vector3();
            this.camera.getWorldDirection(cameraDirection);
            
            const right = new THREE.Vector3();
            right.crossVectors(cameraDirection, this.camera.up).normalize();
            
            const up = new THREE.Vector3();
            up.crossVectors(right, cameraDirection).normalize();
            
            const movement = new THREE.Vector3();
            movement.addScaledVector(right, moveVector.x * this.moveSpeed);
            movement.addScaledVector(up, moveVector.y * this.moveSpeed);
            movement.addScaledVector(cameraDirection, moveVector.z * this.moveSpeed);
            
            // Scale movement by frame time (assuming 60fps for now)
            movement.multiplyScalar(1/60);
            
            this.target.add(movement);
            this.updateCameraPosition();
        }
    }

    private updateSpherical(): void {
        const offset = new THREE.Vector3().subVectors(this.camera.position, this.target);
        this.spherical.setFromVector3(offset);
    }

    private updateCameraPosition(): void {
        const offset = new THREE.Vector3();
        offset.setFromSpherical(this.spherical);
        this.camera.position.copy(this.target).add(offset);
        this.camera.lookAt(this.target);
    }

    public getTarget(): THREE.Vector3 {
        return this.target.clone();
    }

    public getDistance(): number {
        return this.spherical.radius;
    }
}
