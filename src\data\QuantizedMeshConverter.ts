import { ElevationData, QuantizedMeshData, QuantizedMeshHeader, GeographicBounds, CartesianCoordinate } from '../types/TerrainTypes';

export class QuantizedMeshConverter {
    private static readonly QUANTIZED_MESH_MAX_VALUE = 32767; // 2^15 - 1
    private static readonly EARTH_RADIUS = 6371000; // meters

    /**
     * Convert elevation data to Cesium Quantized Mesh format
     */
    public static convertToQuantizedMesh(elevationData: ElevationData): QuantizedMeshData {
        const { width, height, bounds, elevations } = elevationData;
        
        // Calculate mesh statistics
        const stats = this.calculateMeshStatistics(elevations, bounds);
        
        // Generate vertices
        const vertices = this.generateVertices(width, height, bounds, elevations);
        
        // Generate triangle indices using Delaunay-like triangulation
        const indices = this.generateTriangleIndices(width, height);
        
        // Quantize coordinates
        const quantizedVertices = this.quantizeVertices(vertices, bounds, stats.minHeight, stats.maxHeight);
        
        // Create header
        const header = this.createMeshHeader(bounds, stats, vertices);
        
        return {
            header,
            vertexData: {
                vertexCount: vertices.length,
                u: quantizedVertices.u,
                v: quantizedVertices.v,
                height: quantizedVertices.height
            },
            indexData: {
                triangleCount: indices.length / 3,
                indices: indices.length > 65535 ? new Uint32Array(indices) : new Uint16Array(indices)
            }
        };
    }

    /**
     * Calculate mesh statistics for header
     */
    private static calculateMeshStatistics(elevations: Float32Array, bounds: GeographicBounds) {
        let minHeight = Infinity;
        let maxHeight = -Infinity;

        for (let i = 0; i < elevations.length; i++) {
            const height = elevations[i];
            minHeight = Math.min(minHeight, height);
            maxHeight = Math.max(maxHeight, height);
        }

        // Calculate center point in Cartesian coordinates
        const centerLon = (bounds.west + bounds.east) / 2;
        const centerLat = (bounds.north + bounds.south) / 2;
        const centerHeight = (minHeight + maxHeight) / 2;
        
        const center = this.geodeticToCartesian(centerLon, centerLat, centerHeight);

        return {
            minHeight,
            maxHeight,
            center
        };
    }

    /**
     * Generate 3D vertices from elevation grid
     */
    private static generateVertices(
        width: number, 
        height: number, 
        bounds: GeographicBounds, 
        elevations: Float32Array
    ): CartesianCoordinate[] {
        const vertices: CartesianCoordinate[] = [];
        
        for (let y = 0; y < height; y++) {
            for (let x = 0; x < width; x++) {
                const lon = bounds.west + (x / (width - 1)) * (bounds.east - bounds.west);
                const lat = bounds.north - (y / (height - 1)) * (bounds.north - bounds.south);
                const elevation = elevations[y * width + x];
                
                const cartesian = this.geodeticToCartesian(lon, lat, elevation);
                vertices.push(cartesian);
            }
        }
        
        return vertices;
    }

    /**
     * Generate triangle indices for a regular grid
     */
    private static generateTriangleIndices(width: number, height: number): number[] {
        const indices: number[] = [];
        
        for (let y = 0; y < height - 1; y++) {
            for (let x = 0; x < width - 1; x++) {
                const topLeft = y * width + x;
                const topRight = topLeft + 1;
                const bottomLeft = (y + 1) * width + x;
                const bottomRight = bottomLeft + 1;
                
                // First triangle (top-left, bottom-left, top-right)
                indices.push(topLeft, bottomLeft, topRight);
                
                // Second triangle (top-right, bottom-left, bottom-right)
                indices.push(topRight, bottomLeft, bottomRight);
            }
        }
        
        return indices;
    }

    /**
     * Quantize vertices to 16-bit values
     */
    private static quantizeVertices(
        vertices: CartesianCoordinate[],
        bounds: GeographicBounds,
        minHeight: number,
        maxHeight: number
    ) {
        const vertexCount = vertices.length;
        const u = new Uint16Array(vertexCount);
        const v = new Uint16Array(vertexCount);
        const height = new Uint16Array(vertexCount);

        for (let i = 0; i < vertexCount; i++) {
            const vertex = vertices[i];
            
            // Convert back to geodetic for quantization
            const geodetic = this.cartesianToGeodetic(vertex);
            
            // Quantize longitude (u)
            const uNormalized = (geodetic.longitude - bounds.west) / (bounds.east - bounds.west);
            u[i] = Math.round(uNormalized * this.QUANTIZED_MESH_MAX_VALUE);
            
            // Quantize latitude (v)
            const vNormalized = (geodetic.latitude - bounds.south) / (bounds.north - bounds.south);
            v[i] = Math.round(vNormalized * this.QUANTIZED_MESH_MAX_VALUE);
            
            // Quantize height
            const heightNormalized = (geodetic.height - minHeight) / (maxHeight - minHeight);
            height[i] = Math.round(heightNormalized * this.QUANTIZED_MESH_MAX_VALUE);
        }

        return { u, v, height };
    }

    /**
     * Create quantized mesh header
     */
    private static createMeshHeader(
        bounds: GeographicBounds,
        stats: { minHeight: number, maxHeight: number, center: CartesianCoordinate },
        vertices: CartesianCoordinate[]
    ): QuantizedMeshHeader {
        // Calculate bounding sphere
        const boundingSphere = this.calculateBoundingSphere(vertices, stats.center);
        
        // Calculate horizon occlusion point (simplified)
        const horizonOcclusion = this.calculateHorizonOcclusionPoint(stats.center);

        return {
            centerX: stats.center.x,
            centerY: stats.center.y,
            centerZ: stats.center.z,
            minimumHeight: stats.minHeight,
            maximumHeight: stats.maxHeight,
            boundingSphereCenterX: boundingSphere.center.x,
            boundingSphereCenterY: boundingSphere.center.y,
            boundingSphereCenterZ: boundingSphere.center.z,
            boundingSphereRadius: boundingSphere.radius,
            horizonOcclusionPointX: horizonOcclusion.x,
            horizonOcclusionPointY: horizonOcclusion.y,
            horizonOcclusionPointZ: horizonOcclusion.z
        };
    }

    /**
     * Convert geodetic coordinates to Cartesian (ECEF)
     */
    private static geodeticToCartesian(longitude: number, latitude: number, height: number): CartesianCoordinate {
        const lonRad = longitude * Math.PI / 180;
        const latRad = latitude * Math.PI / 180;
        const radius = this.EARTH_RADIUS + height;

        return {
            x: radius * Math.cos(latRad) * Math.cos(lonRad),
            y: radius * Math.cos(latRad) * Math.sin(lonRad),
            z: radius * Math.sin(latRad)
        };
    }

    /**
     * Convert Cartesian coordinates to geodetic
     */
    private static cartesianToGeodetic(cartesian: CartesianCoordinate): { longitude: number, latitude: number, height: number } {
        const x = cartesian.x;
        const y = cartesian.y;
        const z = cartesian.z;

        const longitude = Math.atan2(y, x) * 180 / Math.PI;
        const latitude = Math.atan2(z, Math.sqrt(x * x + y * y)) * 180 / Math.PI;
        const height = Math.sqrt(x * x + y * y + z * z) - this.EARTH_RADIUS;

        return { longitude, latitude, height };
    }

    /**
     * Calculate bounding sphere for vertices
     */
    private static calculateBoundingSphere(vertices: CartesianCoordinate[], center: CartesianCoordinate) {
        let maxDistanceSquared = 0;

        for (const vertex of vertices) {
            const dx = vertex.x - center.x;
            const dy = vertex.y - center.y;
            const dz = vertex.z - center.z;
            const distanceSquared = dx * dx + dy * dy + dz * dz;
            maxDistanceSquared = Math.max(maxDistanceSquared, distanceSquared);
        }

        return {
            center,
            radius: Math.sqrt(maxDistanceSquared)
        };
    }

    /**
     * Calculate horizon occlusion point (simplified implementation)
     */
    private static calculateHorizonOcclusionPoint(center: CartesianCoordinate): CartesianCoordinate {
        // Simplified: use the center point projected to Earth surface
        const length = Math.sqrt(center.x * center.x + center.y * center.y + center.z * center.z);
        const scale = this.EARTH_RADIUS / length;

        return {
            x: center.x * scale,
            y: center.y * scale,
            z: center.z * scale
        };
    }
}
