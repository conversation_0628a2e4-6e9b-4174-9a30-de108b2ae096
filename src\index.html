<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DreaMatch Terrain System</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background: #000;
            font-family: Arial, sans-serif;
        }
        
        #container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #canvas {
            display: block;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 100;
        }
        
        #controls {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
        <div id="info">
            <div>DreaMatch Terrain System v0.0.4</div>
            <div id="fps">FPS: --</div>
            <div id="tiles">Tiles: --</div>
            <div id="memory">Memory: --</div>
        </div>
        <div id="controls">
            <div>Controls:</div>
            <div>Mouse: Orbit camera</div>
            <div>Wheel: Zoom</div>
            <div>WASD: Move</div>
        </div>
    </div>
    <script type="module" src="./main.ts"></script>
</body>
</html>
