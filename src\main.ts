import * as THREE from 'three';
import { TerrainSystem } from './terrain/TerrainSystem';
import { CameraController } from './utils/CameraController';
import { PerformanceMonitor } from './utils/PerformanceMonitor';

class App {
    private scene: THREE.Scene;
    private camera: THREE.PerspectiveCamera;
    private renderer: THREE.WebGLRenderer;
    private terrainSystem: TerrainSystem;
    private cameraController: CameraController;
    private performanceMonitor: PerformanceMonitor;
    private canvas: HTMLCanvasElement;

    constructor() {
        this.canvas = document.getElementById('canvas') as HTMLCanvasElement;
        this.init();
        this.setupEventListeners();
        this.animate();
    }

    private init(): void {
        // Scene setup
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x000011);

        // Camera setup
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            10000000 // Large far plane for planetary scale
        );
        this.camera.position.set(0, 0, 6371000 * 2); // Start at 2 Earth radii

        // Renderer setup
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            antialias: true,
            logarithmicDepthBuffer: true // Important for planetary scale
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

        // Initialize systems
        this.terrainSystem = new TerrainSystem(this.scene, this.camera);
        this.cameraController = new CameraController(this.camera, this.canvas);
        this.performanceMonitor = new PerformanceMonitor();

        // Add some basic lighting
        const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(ambientLight);

        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 1, 0.5).normalize();
        this.scene.add(directionalLight);

        console.log('DreaMatch Terrain System initialized');
    }

    private setupEventListeners(): void {
        window.addEventListener('resize', this.onWindowResize.bind(this));
        
        // Keyboard controls
        window.addEventListener('keydown', (event) => {
            this.cameraController.onKeyDown(event);
        });
        
        window.addEventListener('keyup', (event) => {
            this.cameraController.onKeyUp(event);
        });
    }

    private onWindowResize(): void {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    private animate(): void {
        requestAnimationFrame(this.animate.bind(this));

        // Update systems
        this.cameraController.update();
        this.terrainSystem.update(this.camera);
        this.performanceMonitor.update();

        // Update UI
        this.updateUI();

        // Render
        this.renderer.render(this.scene, this.camera);
    }

    private updateUI(): void {
        const fpsElement = document.getElementById('fps');
        const tilesElement = document.getElementById('tiles');
        const memoryElement = document.getElementById('memory');

        if (fpsElement) {
            fpsElement.textContent = `FPS: ${this.performanceMonitor.getFPS()}`;
        }

        if (tilesElement) {
            tilesElement.textContent = `Tiles: ${this.terrainSystem.getActiveTileCount()}`;
        }

        if (memoryElement) {
            memoryElement.textContent = `Memory: ${this.terrainSystem.getMemoryUsage()}MB`;
        }
    }
}

// Initialize the application
new App();
