import * as THREE from 'three';
import { QuadtreeNode } from './QuadtreeNode';

export enum CullingResult {
    OUTSIDE = 'outside',    // Completely outside frustum
    INSIDE = 'inside',      // Completely inside frustum
    INTERSECTING = 'intersecting' // Partially inside frustum
}

export class FrustumCuller {
    private frustum: THREE.Frustum;
    private frustumMatrix: THREE.Matrix4;
    
    // Horizon culling for planetary rendering
    private horizonCullingEnabled = true;
    private earthRadius = 6371000;
    
    // Statistics
    private stats = {
        totalNodes: 0,
        culledNodes: 0,
        visibleNodes: 0,
        horizonCulledNodes: 0
    };

    constructor() {
        this.frustum = new THREE.Frustum();
        this.frustumMatrix = new THREE.Matrix4();
    }

    /**
     * Update frustum from camera
     */
    public updateFrustum(camera: THREE.PerspectiveCamera): void {
        // Update projection matrix
        camera.updateMatrixWorld();
        camera.updateProjectionMatrix();
        
        // Calculate frustum matrix
        this.frustumMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
        this.frustum.setFromProjectionMatrix(this.frustumMatrix);
    }

    /**
     * Test if a node is visible (not culled)
     */
    public isNodeVisible(node: QuadtreeNode, camera: THREE.PerspectiveCamera): CullingResult {
        this.stats.totalNodes++;

        // First check horizon culling for planetary rendering
        if (this.horizonCullingEnabled) {
            const horizonResult = this.testHorizonCulling(node, camera);
            if (horizonResult === CullingResult.OUTSIDE) {
                this.stats.horizonCulledNodes++;
                this.stats.culledNodes++;
                return CullingResult.OUTSIDE;
            }
        }

        // Test frustum culling
        const frustumResult = this.testFrustumCulling(node);
        
        if (frustumResult === CullingResult.OUTSIDE) {
            this.stats.culledNodes++;
        } else {
            this.stats.visibleNodes++;
        }

        return frustumResult;
    }

    /**
     * Test frustum culling against node's bounding sphere
     */
    private testFrustumCulling(node: QuadtreeNode): CullingResult {
        const sphere = node.boundingSphere;
        
        // Test against each frustum plane
        let inside = true;
        let intersecting = false;

        for (let i = 0; i < 6; i++) {
            const plane = this.frustum.planes[i];
            const distance = plane.distanceToPoint(sphere.center);
            
            if (distance < -sphere.radius) {
                // Completely outside this plane
                return CullingResult.OUTSIDE;
            } else if (distance < sphere.radius) {
                // Intersecting this plane
                intersecting = true;
                inside = false;
            }
        }

        return inside ? CullingResult.INSIDE : 
               intersecting ? CullingResult.INTERSECTING : 
               CullingResult.OUTSIDE;
    }

    /**
     * Test horizon culling for planetary rendering
     * Cull tiles that are behind the horizon from camera's perspective
     */
    private testHorizonCulling(node: QuadtreeNode, camera: THREE.PerspectiveCamera): CullingResult {
        const cameraPosition = camera.position;
        const cameraDistance = cameraPosition.length();
        
        // If camera is very close to surface, disable horizon culling
        if (cameraDistance < this.earthRadius + 1000) {
            return CullingResult.INTERSECTING;
        }

        const nodeCenter = node.boundingSphere.center;
        const nodeRadius = node.boundingSphere.radius;
        
        // Calculate horizon distance
        const horizonDistance = this.calculateHorizonDistance(cameraDistance);
        
        // Vector from camera to node center
        const cameraToNode = new THREE.Vector3().subVectors(nodeCenter, cameraPosition);
        const distanceToNode = cameraToNode.length();
        
        // Project node center onto camera-earth center line
        const earthCenter = new THREE.Vector3(0, 0, 0);
        const cameraToEarth = new THREE.Vector3().subVectors(earthCenter, cameraPosition).normalize();
        const projectionLength = cameraToNode.dot(cameraToEarth);
        
        // If node is behind camera relative to earth, it might be over horizon
        if (projectionLength > horizonDistance + nodeRadius) {
            return CullingResult.OUTSIDE;
        }

        // More precise horizon test using tangent calculation
        return this.preciseHorizonTest(cameraPosition, nodeCenter, nodeRadius);
    }

    /**
     * More precise horizon culling test
     */
    private preciseHorizonTest(
        cameraPosition: THREE.Vector3,
        nodeCenter: THREE.Vector3,
        nodeRadius: number
    ): CullingResult {
        const earthCenter = new THREE.Vector3(0, 0, 0);
        
        // Calculate tangent points from camera to earth sphere
        const cameraDistance = cameraPosition.length();
        const tangentLength = Math.sqrt(cameraDistance * cameraDistance - this.earthRadius * this.earthRadius);
        
        // Angle from camera-earth line to tangent
        const tangentAngle = Math.asin(this.earthRadius / cameraDistance);
        
        // Vector from camera to earth center
        const cameraToEarth = new THREE.Vector3().subVectors(earthCenter, cameraPosition);
        
        // Vector from camera to node
        const cameraToNode = new THREE.Vector3().subVectors(nodeCenter, cameraPosition);
        
        // Angle between camera-earth and camera-node vectors
        const nodeAngle = cameraToEarth.angleTo(cameraToNode);
        
        // If node angle is greater than tangent angle (plus some margin for node radius)
        const nodeAngularSize = Math.atan(nodeRadius / cameraToNode.length());
        
        if (nodeAngle > tangentAngle + nodeAngularSize) {
            return CullingResult.OUTSIDE;
        }
        
        return CullingResult.INTERSECTING;
    }

    /**
     * Calculate horizon distance from camera
     */
    private calculateHorizonDistance(cameraDistance: number): number {
        if (cameraDistance <= this.earthRadius) {
            return 0;
        }
        
        return Math.sqrt(cameraDistance * cameraDistance - this.earthRadius * this.earthRadius);
    }

    /**
     * Cull a list of nodes and return visible ones
     */
    public cullNodes(nodes: QuadtreeNode[], camera: THREE.PerspectiveCamera): {
        visible: QuadtreeNode[];
        culled: QuadtreeNode[];
        inside: QuadtreeNode[];
        intersecting: QuadtreeNode[];
    } {
        const visible: QuadtreeNode[] = [];
        const culled: QuadtreeNode[] = [];
        const inside: QuadtreeNode[] = [];
        const intersecting: QuadtreeNode[] = [];

        for (const node of nodes) {
            const result = this.isNodeVisible(node, camera);
            
            switch (result) {
                case CullingResult.INSIDE:
                    visible.push(node);
                    inside.push(node);
                    node.isVisible = true;
                    break;
                case CullingResult.INTERSECTING:
                    visible.push(node);
                    intersecting.push(node);
                    node.isVisible = true;
                    break;
                case CullingResult.OUTSIDE:
                    culled.push(node);
                    node.isVisible = false;
                    break;
            }
        }

        return { visible, culled, inside, intersecting };
    }

    /**
     * Enable or disable horizon culling
     */
    public setHorizonCullingEnabled(enabled: boolean): void {
        this.horizonCullingEnabled = enabled;
    }

    /**
     * Set earth radius for horizon culling
     */
    public setEarthRadius(radius: number): void {
        this.earthRadius = radius;
    }

    /**
     * Get culling statistics
     */
    public getStats(): {
        totalNodes: number;
        culledNodes: number;
        visibleNodes: number;
        horizonCulledNodes: number;
        cullingRatio: number;
    } {
        const cullingRatio = this.stats.totalNodes > 0 ? 
            this.stats.culledNodes / this.stats.totalNodes : 0;

        return {
            ...this.stats,
            cullingRatio
        };
    }

    /**
     * Reset statistics
     */
    public resetStats(): void {
        this.stats = {
            totalNodes: 0,
            culledNodes: 0,
            visibleNodes: 0,
            horizonCulledNodes: 0
        };
    }

    /**
     * Get frustum planes for debugging
     */
    public getFrustumPlanes(): THREE.Plane[] {
        return this.frustum.planes;
    }

    /**
     * Test if a point is inside the frustum
     */
    public containsPoint(point: THREE.Vector3): boolean {
        return this.frustum.containsPoint(point);
    }

    /**
     * Test if a sphere intersects the frustum
     */
    public intersectsSphere(sphere: THREE.Sphere): boolean {
        return this.frustum.intersectsSphere(sphere);
    }

    /**
     * Test if a box intersects the frustum
     */
    public intersectsBox(box: THREE.Box3): boolean {
        return this.frustum.intersectsBox(box);
    }
}
