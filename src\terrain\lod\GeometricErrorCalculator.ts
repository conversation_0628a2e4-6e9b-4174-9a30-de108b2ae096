import * as THREE from 'three';
import { QuadtreeNode } from './QuadtreeNode';

export class GeometricErrorCalculator {
    private static readonly DEFAULT_ERROR_THRESHOLD = 2.0; // pixels
    private static readonly MIN_ERROR_THRESHOLD = 0.5;
    private static readonly MAX_ERROR_THRESHOLD = 16.0;

    /**
     * Calculate projected geometric error (screen-space error) for a node
     * Based on <PERSON><PERSON>'s approach and chunked LOD literature
     */
    public static calculateScreenSpaceError(
        node: QuadtreeNode,
        camera: THREE.PerspectiveCamera,
        viewportWidth: number,
        viewportHeight: number
    ): number {
        // Get camera position
        const cameraPosition = camera.position;
        
        // Calculate distance from camera to node's bounding sphere surface
        const distanceToSphere = Math.max(0, node.boundingSphere.distanceToPoint(cameraPosition));
        
        // If camera is inside the bounding sphere, error is maximum
        if (distanceToSphere === 0) {
            return Number.MAX_VALUE;
        }

        // Calculate the projected size of the geometric error
        const geometricError = node.geometricError;
        const fov = camera.fov * Math.PI / 180; // Convert to radians
        const viewportHeight_half = viewportHeight / 2;
        
        // Screen-space error formula: error_pixels = (geometric_error * viewport_height) / (2 * distance * tan(fov/2))
        const screenSpaceError = (geometricError * viewportHeight_half) / (distanceToSphere * Math.tan(fov / 2));
        
        return screenSpaceError;
    }

    /**
     * Calculate adaptive error threshold based on camera altitude and movement
     */
    public static calculateAdaptiveErrorThreshold(
        camera: THREE.PerspectiveCamera,
        cameraVelocity: number = 0,
        baseThreshold: number = GeometricErrorCalculator.DEFAULT_ERROR_THRESHOLD
    ): number {
        const cameraPosition = camera.position;
        const earthRadius = 6371000;
        const altitude = cameraPosition.length() - earthRadius;
        
        // Adjust threshold based on altitude
        let threshold = baseThreshold;
        
        // Higher altitude = can tolerate more error
        if (altitude > 1000000) { // Above 1000km
            threshold *= 4;
        } else if (altitude > 100000) { // Above 100km
            threshold *= 2;
        } else if (altitude < 1000) { // Below 1km
            threshold *= 0.5;
        }
        
        // Adjust for camera movement (higher velocity = can tolerate more error)
        const velocityFactor = Math.min(2, 1 + cameraVelocity / 1000);
        threshold *= velocityFactor;
        
        // Clamp to reasonable bounds
        return Math.max(
            GeometricErrorCalculator.MIN_ERROR_THRESHOLD,
            Math.min(GeometricErrorCalculator.MAX_ERROR_THRESHOLD, threshold)
        );
    }

    /**
     * Calculate distance-based LOD bias
     */
    public static calculateLODBias(
        node: QuadtreeNode,
        camera: THREE.PerspectiveCamera
    ): number {
        const distance = node.distanceToCamera;
        const earthRadius = 6371000;
        
        // Normalize distance (0 = at surface, 1 = at 2 earth radii)
        const normalizedDistance = Math.min(1, distance / (earthRadius * 2));
        
        // Apply bias curve (closer = higher detail)
        return 1 - Math.pow(normalizedDistance, 0.5);
    }

    /**
     * Calculate view-dependent error scaling
     */
    public static calculateViewDependentScaling(
        node: QuadtreeNode,
        camera: THREE.PerspectiveCamera
    ): number {
        const cameraDirection = new THREE.Vector3();
        camera.getWorldDirection(cameraDirection);
        
        const nodeCenter = node.boundingSphere.center;
        const cameraToNode = new THREE.Vector3().subVectors(nodeCenter, camera.position).normalize();
        
        // Calculate angle between camera direction and node direction
        const dotProduct = cameraDirection.dot(cameraToNode);
        
        // Nodes in peripheral vision can have higher error
        const viewAngleFactor = Math.max(0.5, dotProduct);
        
        return viewAngleFactor;
    }

    /**
     * Calculate temporal coherence factor to reduce popping
     */
    public static calculateTemporalCoherence(
        node: QuadtreeNode,
        frameNumber: number,
        hysteresis: number = 0.1
    ): number {
        const framesSinceSelected = frameNumber - node.lastFrameSelected;
        
        // Recently selected nodes get a bias toward staying selected
        if (framesSinceSelected < 10) {
            return 1 - hysteresis;
        }
        
        return 1;
    }

    /**
     * Main function to determine if a node should be subdivided
     */
    public static shouldSubdivide(
        node: QuadtreeNode,
        camera: THREE.PerspectiveCamera,
        viewportWidth: number,
        viewportHeight: number,
        frameNumber: number,
        options: {
            baseErrorThreshold?: number;
            cameraVelocity?: number;
            enableAdaptiveThreshold?: boolean;
            enableViewDependentScaling?: boolean;
            enableTemporalCoherence?: boolean;
        } = {}
    ): boolean {
        // Can't subdivide if at max level
        if (!node.canSubdivide()) {
            return false;
        }

        // Calculate screen-space error
        const screenSpaceError = this.calculateScreenSpaceError(
            node, camera, viewportWidth, viewportHeight
        );
        
        // Store for debugging/visualization
        node.screenSpaceError = screenSpaceError;

        // Calculate error threshold
        let errorThreshold = options.baseErrorThreshold || this.DEFAULT_ERROR_THRESHOLD;
        
        if (options.enableAdaptiveThreshold) {
            errorThreshold = this.calculateAdaptiveErrorThreshold(
                camera, options.cameraVelocity, errorThreshold
            );
        }

        // Apply view-dependent scaling
        if (options.enableViewDependentScaling) {
            const viewScaling = this.calculateViewDependentScaling(node, camera);
            errorThreshold /= viewScaling;
        }

        // Apply temporal coherence
        if (options.enableTemporalCoherence) {
            const temporalFactor = this.calculateTemporalCoherence(node, frameNumber);
            errorThreshold *= temporalFactor;
        }

        return screenSpaceError > errorThreshold;
    }

    /**
     * Calculate priority score for tile loading
     */
    public static calculateLoadingPriority(
        node: QuadtreeNode,
        camera: THREE.PerspectiveCamera,
        viewportWidth: number,
        viewportHeight: number
    ): number {
        const screenSpaceError = this.calculateScreenSpaceError(
            node, camera, viewportWidth, viewportHeight
        );
        
        const distance = node.distanceToCamera;
        const viewDependentScaling = this.calculateViewDependentScaling(node, camera);
        
        // Higher screen space error = higher priority
        // Closer distance = higher priority
        // Better view angle = higher priority
        const priority = (screenSpaceError * viewDependentScaling) / (distance + 1);
        
        return priority;
    }

    /**
     * Get debug information for error calculation
     */
    public static getDebugInfo(
        node: QuadtreeNode,
        camera: THREE.PerspectiveCamera,
        viewportWidth: number,
        viewportHeight: number
    ): {
        screenSpaceError: number;
        geometricError: number;
        distance: number;
        cameraAltitude: number;
        viewAngle: number;
    } {
        const screenSpaceError = this.calculateScreenSpaceError(
            node, camera, viewportWidth, viewportHeight
        );
        
        const earthRadius = 6371000;
        const cameraAltitude = camera.position.length() - earthRadius;
        
        const cameraDirection = new THREE.Vector3();
        camera.getWorldDirection(cameraDirection);
        const nodeCenter = node.boundingSphere.center;
        const cameraToNode = new THREE.Vector3().subVectors(nodeCenter, camera.position).normalize();
        const viewAngle = Math.acos(cameraDirection.dot(cameraToNode)) * 180 / Math.PI;

        return {
            screenSpaceError,
            geometricError: node.geometricError,
            distance: node.distanceToCamera,
            cameraAltitude,
            viewAngle
        };
    }
}
