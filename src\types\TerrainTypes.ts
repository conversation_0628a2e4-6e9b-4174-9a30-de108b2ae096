// Core terrain data structures

export interface TileCoordinate {
    x: number;
    y: number;
    z: number; // zoom level
}

export interface GeographicBounds {
    north: number;
    south: number;
    east: number;
    west: number;
}

export interface ElevationData {
    width: number;
    height: number;
    bounds: GeographicBounds;
    elevations: Float32Array;
    resolution: number; // meters per pixel
}

export interface TileMetadata {
    coordinate: TileCoordinate;
    bounds: GeographicBounds;
    geometricError: number;
    hasChildren: boolean;
    dataUrl?: string;
}

export interface QuantizedMeshHeader {
    centerX: number;
    centerY: number;
    centerZ: number;
    minimumHeight: number;
    maximumHeight: number;
    boundingSphereCenterX: number;
    boundingSphereCenterY: number;
    boundingSphereCenterZ: number;
    boundingSphereRadius: number;
    horizonOcclusionPointX: number;
    horizonOcclusionPointY: number;
    horizonOcclusionPointZ: number;
}

export interface QuantizedMeshData {
    header: QuantizedMeshHeader;
    vertexData: {
        vertexCount: number;
        u: Uint16Array; // quantized longitude
        v: Uint16Array; // quantized latitude
        height: Uint16Array; // quantized height
    };
    indexData: {
        triangleCount: number;
        indices: Uint16Array | Uint32Array;
    };
    edgeIndices?: {
        westVertexCount: number;
        westIndices: Uint16Array;
        southVertexCount: number;
        southIndices: Uint16Array;
        eastVertexCount: number;
        eastIndices: Uint16Array;
        northVertexCount: number;
        northIndices: Uint16Array;
    };
}

export interface LayerMetadata {
    name: string;
    description: string;
    version: string;
    format: string;
    bounds: GeographicBounds;
    minzoom: number;
    maxzoom: number;
    tiles: string[];
    attribution: string;
    scheme: 'xyz' | 'tms';
    tileSize: number;
    geometricErrorByLevel: number[];
}

export interface TerrainTile {
    coordinate: TileCoordinate;
    bounds: GeographicBounds;
    geometricError: number;
    mesh?: QuantizedMeshData;
    children?: TileCoordinate[];
    parent?: TileCoordinate;
    isLoaded: boolean;
    isLoading: boolean;
    lastAccessed: number;
}

// Open-Meteo API types
export interface OpenMeteoElevationRequest {
    latitude: number[];
    longitude: number[];
    format?: 'json';
}

export interface OpenMeteoElevationResponse {
    latitude: number[];
    longitude: number[];
    elevation: number[];
    utc_offset_seconds: number;
    timezone: string;
    timezone_abbreviation: string;
}

// Utility types
export interface Point2D {
    x: number;
    y: number;
}

export interface Point3D {
    x: number;
    y: number;
    z: number;
}

export interface CartesianCoordinate {
    x: number;
    y: number;
    z: number;
}

export interface GeodeticCoordinate {
    longitude: number;
    latitude: number;
    height: number;
}
