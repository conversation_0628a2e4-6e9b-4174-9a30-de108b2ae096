import { LayerMetadata, GeographicBounds } from '../types/TerrainTypes';
import { TilePyramidGenerator } from './TilePyramidGenerator';

export class LayerMetadataProducer {
    private static readonly VERSION = '1.0.0';
    private static readonly FORMAT = 'quantized-mesh-1.0';
    private static readonly TILE_SIZE = 65; // Standard for terrain tiles
    private static readonly SCHEME = 'tms' as const; // Tile Map Service scheme

    /**
     * Generate complete layer metadata for the terrain system
     */
    public static generateLayerMetadata(baseUrl: string = ''): LayerMetadata {
        const bounds = this.getGlobalBounds();
        const geometricErrorByLevel = this.calculateGeometricErrorByLevel();
        const tiles = this.generateTileTemplates(baseUrl);

        return {
            name: 'DreaMatch Global Terrain',
            description: 'Multiresolution tile pyramid terrain system using Copernicus 90m DEM data',
            version: this.VERSION,
            format: this.FORMAT,
            bounds,
            minzoom: 0,
            maxzoom: 14,
            tiles,
            attribution: 'Elevation data from Copernicus DEM via Open-Meteo API',
            scheme: this.SCHEME,
            tileSize: this.TILE_SIZE,
            geometricErrorByLevel
        };
    }

    /**
     * Get global geographic bounds
     */
    private static getGlobalBounds(): GeographicBounds {
        return {
            west: -180,
            east: 180,
            north: 90,
            south: -90
        };
    }

    /**
     * Calculate geometric error for each zoom level
     */
    private static calculateGeometricErrorByLevel(): number[] {
        const errors: number[] = [];
        
        for (let z = 0; z <= 14; z++) {
            const error = TilePyramidGenerator.calculateGeometricError(z);
            errors.push(error);
        }
        
        return errors;
    }

    /**
     * Generate tile URL templates
     */
    private static generateTileTemplates(baseUrl: string): string[] {
        const templates: string[] = [];
        
        // Standard tile URL template
        const template = baseUrl ? 
            `${baseUrl}/tiles/{z}/{x}/{y}.terrain` :
            './tiles/{z}/{x}/{y}.terrain';
            
        templates.push(template);
        
        return templates;
    }

    /**
     * Generate metadata for a specific tile
     */
    public static generateTileMetadata(z: number, x: number, y: number) {
        const bounds = TilePyramidGenerator.getTileBounds({ x, y, z });
        const geometricError = TilePyramidGenerator.calculateGeometricError(z);
        const hasChildren = z < 14;
        const children = hasChildren ? TilePyramidGenerator.getChildTiles({ x, y, z }) : [];

        return {
            coordinate: { x, y, z },
            bounds,
            geometricError,
            hasChildren,
            children: children.map(child => ({
                x: child.x,
                y: child.y,
                z: child.z
            })),
            parent: z > 0 ? TilePyramidGenerator.getParentTile({ x, y, z }) : null,
            neighbors: TilePyramidGenerator.getNeighborTiles({ x, y, z })
        };
    }

    /**
     * Generate tile matrix metadata for each zoom level
     */
    public static generateTileMatrix(): Array<{
        zoom: number;
        tilesX: number;
        tilesY: number;
        pixelSize: number;
        geometricError: number;
    }> {
        const matrix = [];
        
        for (let z = 0; z <= 14; z++) {
            const tilesPerSide = Math.pow(2, z);
            const pixelSize = 360 / (tilesPerSide * this.TILE_SIZE); // degrees per pixel
            const geometricError = TilePyramidGenerator.calculateGeometricError(z);
            
            matrix.push({
                zoom: z,
                tilesX: tilesPerSide,
                tilesY: tilesPerSide,
                pixelSize,
                geometricError
            });
        }
        
        return matrix;
    }

    /**
     * Generate availability metadata (which tiles exist at each level)
     */
    public static generateAvailabilityMetadata(): Map<number, Set<string>> {
        const availability = new Map<number, Set<string>>();
        
        for (let z = 0; z <= 14; z++) {
            const tiles = TilePyramidGenerator.getTilesAtZoomLevel(z);
            const tileKeys = new Set(tiles.map(tile => `${tile.x},${tile.y}`));
            availability.set(z, tileKeys);
        }
        
        return availability;
    }

    /**
     * Calculate memory requirements for different zoom levels
     */
    public static calculateMemoryRequirements(): Array<{
        zoom: number;
        tilesCount: number;
        estimatedMemoryMB: number;
        recommendedCacheSize: number;
    }> {
        const requirements = [];
        const avgTileSizeKB = 50; // Estimated average quantized mesh size
        
        for (let z = 0; z <= 14; z++) {
            const tilesCount = Math.pow(4, z); // 4^z tiles at level z
            const totalMemoryMB = (tilesCount * avgTileSizeKB) / 1024;
            
            // Recommend caching 10% of tiles at this level, minimum 100, maximum 10000
            const recommendedCacheSize = Math.min(10000, Math.max(100, Math.floor(tilesCount * 0.1)));
            
            requirements.push({
                zoom: z,
                tilesCount,
                estimatedMemoryMB: Math.round(totalMemoryMB * 100) / 100,
                recommendedCacheSize
            });
        }
        
        return requirements;
    }

    /**
     * Generate performance recommendations based on device capabilities
     */
    public static generatePerformanceRecommendations(deviceInfo?: {
        gpuMemoryMB?: number;
        isMobile?: boolean;
        webGPUSupported?: boolean;
    }) {
        const recommendations = {
            maxConcurrentTiles: 1000,
            maxZoomLevel: 14,
            prefetchRadius: 2,
            memoryBudgetMB: 256,
            enableLODBias: false,
            useWebGPU: false
        };

        if (deviceInfo) {
            // Adjust based on GPU memory
            if (deviceInfo.gpuMemoryMB) {
                if (deviceInfo.gpuMemoryMB >= 8192) {
                    recommendations.maxConcurrentTiles = 2000;
                    recommendations.memoryBudgetMB = 512;
                } else if (deviceInfo.gpuMemoryMB >= 4096) {
                    recommendations.maxConcurrentTiles = 1500;
                    recommendations.memoryBudgetMB = 384;
                } else if (deviceInfo.gpuMemoryMB < 2048) {
                    recommendations.maxConcurrentTiles = 500;
                    recommendations.memoryBudgetMB = 128;
                }
            }

            // Adjust for mobile devices
            if (deviceInfo.isMobile) {
                recommendations.maxConcurrentTiles = Math.floor(recommendations.maxConcurrentTiles * 0.6);
                recommendations.memoryBudgetMB = Math.floor(recommendations.memoryBudgetMB * 0.7);
                recommendations.prefetchRadius = 1;
                recommendations.enableLODBias = true;
            }

            // Enable WebGPU if supported
            if (deviceInfo.webGPUSupported) {
                recommendations.useWebGPU = true;
            }
        }

        return recommendations;
    }

    /**
     * Export layer metadata as JSON
     */
    public static exportLayerMetadata(metadata: LayerMetadata): string {
        return JSON.stringify(metadata, null, 2);
    }

    /**
     * Validate layer metadata
     */
    public static validateLayerMetadata(metadata: LayerMetadata): { valid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!metadata.name) errors.push('Missing layer name');
        if (!metadata.format) errors.push('Missing format specification');
        if (metadata.minzoom < 0) errors.push('Invalid minimum zoom level');
        if (metadata.maxzoom > 20) errors.push('Maximum zoom level too high');
        if (metadata.tiles.length === 0) errors.push('No tile templates specified');
        
        if (!metadata.bounds) {
            errors.push('Missing bounds specification');
        } else {
            if (metadata.bounds.west >= metadata.bounds.east) {
                errors.push('Invalid longitude bounds');
            }
            if (metadata.bounds.south >= metadata.bounds.north) {
                errors.push('Invalid latitude bounds');
            }
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }
}
