import { TileCoordinate, GeographicBounds, ElevationData, TileMetadata } from '../types/TerrainTypes';

export class TilePyramidGenerator {
    private static readonly MAX_ZOOM_LEVEL = 14;
    private static readonly MIN_ZOOM_LEVEL = 0;
    private static readonly EARTH_RADIUS = 6371000; // meters

    /**
     * Generate tile pyramid following Mapbox tile scheme
     * Z0 = 1 tile (whole earth), Z1 = 4 tiles, Z2 = 16 tiles, etc.
     */
    public static generateTilePyramid(): Map<number, TileCoordinate[]> {
        const pyramid = new Map<number, TileCoordinate[]>();

        for (let z = this.MIN_ZOOM_LEVEL; z <= this.MAX_ZOOM_LEVEL; z++) {
            const tilesAtLevel = this.getTilesAtZoomLevel(z);
            pyramid.set(z, tilesAtLevel);
            console.log(`Zoom level ${z}: ${tilesAtLevel.length} tiles`);
        }

        return pyramid;
    }

    /**
     * Get all tile coordinates for a specific zoom level
     */
    public static getTilesAtZoomLevel(zoomLevel: number): TileCoordinate[] {
        const tiles: TileCoordinate[] = [];
        const tilesPerSide = Math.pow(2, zoomLevel);

        for (let x = 0; x < tilesPerSide; x++) {
            for (let y = 0; y < tilesPerSide; y++) {
                tiles.push({ x, y, z: zoomLevel });
            }
        }

        return tiles;
    }

    /**
     * Get geographic bounds for a tile coordinate
     */
    public static getTileBounds(coord: TileCoordinate): GeographicBounds {
        const tilesPerSide = Math.pow(2, coord.z);
        const tileWidth = 360 / tilesPerSide;
        const tileHeight = 180 / tilesPerSide;

        return {
            west: -180 + coord.x * tileWidth,
            east: -180 + (coord.x + 1) * tileWidth,
            north: 90 - coord.y * tileHeight,
            south: 90 - (coord.y + 1) * tileHeight
        };
    }

    /**
     * Get parent tile coordinate
     */
    public static getParentTile(coord: TileCoordinate): TileCoordinate | null {
        if (coord.z <= this.MIN_ZOOM_LEVEL) {
            return null;
        }

        return {
            x: Math.floor(coord.x / 2),
            y: Math.floor(coord.y / 2),
            z: coord.z - 1
        };
    }

    /**
     * Get child tile coordinates
     */
    public static getChildTiles(coord: TileCoordinate): TileCoordinate[] {
        if (coord.z >= this.MAX_ZOOM_LEVEL) {
            return [];
        }

        const childZ = coord.z + 1;
        const baseX = coord.x * 2;
        const baseY = coord.y * 2;

        return [
            { x: baseX, y: baseY, z: childZ },
            { x: baseX + 1, y: baseY, z: childZ },
            { x: baseX, y: baseY + 1, z: childZ },
            { x: baseX + 1, y: baseY + 1, z: childZ }
        ];
    }

    /**
     * Calculate geometric error for a tile based on zoom level
     * This follows the Cesium approach for screen-space error calculation
     */
    public static calculateGeometricError(zoomLevel: number): number {
        // Base geometric error at zoom level 0 (whole earth)
        const baseError = this.EARTH_RADIUS * Math.PI; // Half circumference
        
        // Each zoom level halves the geometric error
        return baseError / Math.pow(2, zoomLevel);
    }

    /**
     * Resample elevation data to a lower resolution for parent tiles
     */
    public static resampleElevationData(
        sourceData: ElevationData,
        targetWidth: number,
        targetHeight: number
    ): ElevationData {
        const sourceWidth = sourceData.width;
        const sourceHeight = sourceData.height;
        const sourceElevations = sourceData.elevations;
        
        const targetElevations = new Float32Array(targetWidth * targetHeight);
        
        const xRatio = sourceWidth / targetWidth;
        const yRatio = sourceHeight / targetHeight;

        for (let y = 0; y < targetHeight; y++) {
            for (let x = 0; x < targetWidth; x++) {
                const sourceX = x * xRatio;
                const sourceY = y * yRatio;
                
                // Bilinear interpolation
                const elevation = this.bilinearInterpolation(
                    sourceElevations,
                    sourceWidth,
                    sourceHeight,
                    sourceX,
                    sourceY
                );
                
                targetElevations[y * targetWidth + x] = elevation;
            }
        }

        return {
            width: targetWidth,
            height: targetHeight,
            bounds: sourceData.bounds,
            elevations: targetElevations,
            resolution: sourceData.resolution * Math.max(xRatio, yRatio)
        };
    }

    /**
     * Bilinear interpolation for smooth resampling
     */
    private static bilinearInterpolation(
        data: Float32Array,
        width: number,
        height: number,
        x: number,
        y: number
    ): number {
        const x1 = Math.floor(x);
        const y1 = Math.floor(y);
        const x2 = Math.min(x1 + 1, width - 1);
        const y2 = Math.min(y1 + 1, height - 1);

        const fx = x - x1;
        const fy = y - y1;

        const getValue = (px: number, py: number): number => {
            const index = py * width + px;
            return index < data.length ? data[index] : 0;
        };

        const a = getValue(x1, y1);
        const b = getValue(x2, y1);
        const c = getValue(x1, y2);
        const d = getValue(x2, y2);

        const i1 = a * (1 - fx) + b * fx;
        const i2 = c * (1 - fx) + d * fx;

        return i1 * (1 - fy) + i2 * fy;
    }

    /**
     * Generate tile metadata for the pyramid
     */
    public static generateTileMetadata(coord: TileCoordinate): TileMetadata {
        const bounds = this.getTileBounds(coord);
        const geometricError = this.calculateGeometricError(coord.z);
        const hasChildren = coord.z < this.MAX_ZOOM_LEVEL;

        return {
            coordinate: coord,
            bounds,
            geometricError,
            hasChildren
        };
    }

    /**
     * Get neighboring tiles at the same zoom level
     */
    public static getNeighborTiles(coord: TileCoordinate): TileCoordinate[] {
        const neighbors: TileCoordinate[] = [];
        const tilesPerSide = Math.pow(2, coord.z);

        // Define neighbor offsets (8-connected)
        const offsets = [
            [-1, -1], [0, -1], [1, -1],
            [-1,  0],          [1,  0],
            [-1,  1], [0,  1], [1,  1]
        ];

        for (const [dx, dy] of offsets) {
            const nx = coord.x + dx;
            const ny = coord.y + dy;

            // Handle wrapping for longitude (x-axis)
            const wrappedX = ((nx % tilesPerSide) + tilesPerSide) % tilesPerSide;

            // Check bounds for latitude (y-axis)
            if (ny >= 0 && ny < tilesPerSide) {
                neighbors.push({
                    x: wrappedX,
                    y: ny,
                    z: coord.z
                });
            }
        }

        return neighbors;
    }

    /**
     * Convert tile coordinate to string key for caching
     */
    public static tileToKey(coord: TileCoordinate): string {
        return `${coord.z}/${coord.x}/${coord.y}`;
    }

    /**
     * Parse tile key back to coordinate
     */
    public static keyToTile(key: string): TileCoordinate {
        const [z, x, y] = key.split('/').map(Number);
        return { x, y, z };
    }
}
