import * as THREE from 'three';
import { QuadtreeNode, NodeState } from './QuadtreeNode';
import { GeometricErrorCalculator } from './GeometricErrorCalculator';
import { FrustumCuller, CullingResult } from './FrustumCuller';
import { TileCoordinate } from '../../types/TerrainTypes';

export interface LODSelectionOptions {
    errorThreshold: number;
    maxConcurrentLoads: number;
    enableAdaptiveThreshold: boolean;
    enableViewDependentScaling: boolean;
    enableTemporalCoherence: boolean;
    enableHorizonCulling: boolean;
    prefetchRadius: number;
    memoryBudgetMB: number;
}

export interface LODSelectionResult {
    selectedNodes: QuadtreeNode[];
    loadingNodes: QuadtreeNode[];
    culledNodes: QuadtreeNode[];
    stats: {
        totalNodes: number;
        selectedNodes: number;
        loadingNodes: number;
        culledNodes: number;
        memoryUsageMB: number;
    };
}

export class LODSelector {
    private frustumCuller: FrustumCuller;
    private frameNumber = 0;
    private lastCameraPosition = new THREE.Vector3();
    private cameraVelocity = 0;
    
    // Root nodes for the quadtree (typically 1 for whole earth, or 6 for cube faces)
    private rootNodes: QuadtreeNode[] = [];
    
    // Currently selected nodes for rendering
    private selectedNodes = new Set<QuadtreeNode>();
    private loadingNodes = new Set<QuadtreeNode>();
    
    // Performance tracking
    private memoryUsage = 0; // MB
    private loadingQueue: QuadtreeNode[] = [];

    constructor() {
        this.frustumCuller = new FrustumCuller();
        this.initializeRootNodes();
    }

    /**
     * Initialize root nodes for the quadtree
     */
    private initializeRootNodes(): void {
        // Create root node covering the entire earth at zoom level 0
        const rootCoordinate: TileCoordinate = { x: 0, y: 0, z: 0 };
        const rootNode = new QuadtreeNode(rootCoordinate);
        this.rootNodes.push(rootNode);
    }

    /**
     * Main LOD selection algorithm
     */
    public selectLOD(
        camera: THREE.PerspectiveCamera,
        viewportWidth: number,
        viewportHeight: number,
        options: LODSelectionOptions
    ): LODSelectionResult {
        this.frameNumber++;
        
        // Update camera velocity
        this.updateCameraVelocity(camera);
        
        // Update frustum
        this.frustumCuller.updateFrustum(camera);
        this.frustumCuller.setHorizonCullingEnabled(options.enableHorizonCulling);
        
        // Reset selection
        this.selectedNodes.clear();
        const culledNodes: QuadtreeNode[] = [];
        
        // Traverse quadtree and select nodes
        for (const rootNode of this.rootNodes) {
            this.traverseAndSelect(
                rootNode,
                camera,
                viewportWidth,
                viewportHeight,
                options,
                culledNodes
            );
        }
        
        // Manage loading queue
        this.manageLoadingQueue(options);
        
        // Calculate statistics
        const stats = this.calculateStats();
        
        return {
            selectedNodes: Array.from(this.selectedNodes),
            loadingNodes: Array.from(this.loadingNodes),
            culledNodes,
            stats
        };
    }

    /**
     * Recursively traverse quadtree and select appropriate nodes
     */
    private traverseAndSelect(
        node: QuadtreeNode,
        camera: THREE.PerspectiveCamera,
        viewportWidth: number,
        viewportHeight: number,
        options: LODSelectionOptions,
        culledNodes: QuadtreeNode[]
    ): void {
        // Update node distance to camera
        node.updateDistanceToCamera(camera.position);
        
        // Test frustum culling
        const cullingResult = this.frustumCuller.isNodeVisible(node, camera);
        
        if (cullingResult === CullingResult.OUTSIDE) {
            culledNodes.push(node);
            node.isVisible = false;
            return;
        }
        
        node.isVisible = true;
        
        // Test if node should be subdivided
        const shouldSubdivide = GeometricErrorCalculator.shouldSubdivide(
            node,
            camera,
            viewportWidth,
            viewportHeight,
            this.frameNumber,
            {
                baseErrorThreshold: options.errorThreshold,
                cameraVelocity: this.cameraVelocity,
                enableAdaptiveThreshold: options.enableAdaptiveThreshold,
                enableViewDependentScaling: options.enableViewDependentScaling,
                enableTemporalCoherence: options.enableTemporalCoherence
            }
        );

        if (shouldSubdivide && node.canSubdivide()) {
            // Subdivide if not already done
            if (node.children.length === 0) {
                node.subdivide();
            }
            
            // Check if children are loaded
            let allChildrenReady = true;
            for (const child of node.children) {
                if (child.state !== NodeState.LOADED) {
                    allChildrenReady = false;
                    
                    // Add to loading queue if not already loading
                    if (child.state === NodeState.UNLOADED) {
                        this.addToLoadingQueue(child);
                    }
                }
            }
            
            if (allChildrenReady) {
                // Recurse to children
                for (const child of node.children) {
                    this.traverseAndSelect(
                        child,
                        camera,
                        viewportWidth,
                        viewportHeight,
                        options,
                        culledNodes
                    );
                }
            } else {
                // Use this node as fallback while children load
                this.selectNode(node);
            }
        } else {
            // Select this node for rendering
            this.selectNode(node);
        }
    }

    /**
     * Select a node for rendering
     */
    private selectNode(node: QuadtreeNode): void {
        this.selectedNodes.add(node);
        node.isSelected = true;
        node.lastFrameSelected = this.frameNumber;
        
        // Ensure node is loaded
        if (node.state === NodeState.UNLOADED) {
            this.addToLoadingQueue(node);
        }
    }

    /**
     * Add node to loading queue with priority
     */
    private addToLoadingQueue(node: QuadtreeNode): void {
        if (this.loadingNodes.has(node)) {
            return;
        }
        
        node.state = NodeState.LOADING;
        this.loadingNodes.add(node);
        this.loadingQueue.push(node);
        
        // Sort by priority (closer nodes first)
        this.loadingQueue.sort((a, b) => a.distanceToCamera - b.distanceToCamera);
    }

    /**
     * Manage loading queue based on constraints
     */
    private manageLoadingQueue(options: LODSelectionOptions): void {
        // Limit concurrent loads
        while (this.loadingQueue.length > options.maxConcurrentLoads) {
            const node = this.loadingQueue.pop()!;
            node.state = NodeState.UNLOADED;
            this.loadingNodes.delete(node);
        }
        
        // Check memory budget
        if (this.memoryUsage > options.memoryBudgetMB) {
            this.evictLeastRecentlyUsedNodes();
        }
    }

    /**
     * Evict least recently used nodes to free memory
     */
    private evictLeastRecentlyUsedNodes(): void {
        const allNodes: QuadtreeNode[] = [];
        
        // Collect all loaded nodes
        for (const rootNode of this.rootNodes) {
            rootNode.traverse(node => {
                if (node.state === NodeState.LOADED && !this.selectedNodes.has(node)) {
                    allNodes.push(node);
                }
            });
        }
        
        // Sort by last access time
        allNodes.sort((a, b) => a.lastFrameSelected - b.lastFrameSelected);
        
        // Evict oldest nodes until memory is under budget
        const targetMemory = this.memoryUsage * 0.8; // Target 80% of budget
        let currentMemory = this.memoryUsage;
        
        for (const node of allNodes) {
            if (currentMemory <= targetMemory) {
                break;
            }
            
            // Estimate memory usage (simplified)
            const nodeMemory = 0.05; // 50KB per node estimate
            currentMemory -= nodeMemory;
            
            // Dispose node
            node.dispose();
        }
        
        this.memoryUsage = currentMemory;
    }

    /**
     * Update camera velocity for adaptive thresholding
     */
    private updateCameraVelocity(camera: THREE.PerspectiveCamera): void {
        const currentPosition = camera.position.clone();
        const deltaPosition = currentPosition.distanceTo(this.lastCameraPosition);
        
        // Assume 60 FPS for velocity calculation
        this.cameraVelocity = deltaPosition * 60;
        
        this.lastCameraPosition.copy(currentPosition);
    }

    /**
     * Calculate performance statistics
     */
    private calculateStats(): LODSelectionResult['stats'] {
        let totalNodes = 0;
        
        for (const rootNode of this.rootNodes) {
            rootNode.traverse(() => totalNodes++);
        }
        
        return {
            totalNodes,
            selectedNodes: this.selectedNodes.size,
            loadingNodes: this.loadingNodes.size,
            culledNodes: this.frustumCuller.getStats().culledNodes,
            memoryUsageMB: this.memoryUsage
        };
    }

    /**
     * Get all root nodes
     */
    public getRootNodes(): QuadtreeNode[] {
        return this.rootNodes;
    }

    /**
     * Get currently selected nodes
     */
    public getSelectedNodes(): QuadtreeNode[] {
        return Array.from(this.selectedNodes);
    }

    /**
     * Get loading nodes
     */
    public getLoadingNodes(): QuadtreeNode[] {
        return Array.from(this.loadingNodes);
    }

    /**
     * Mark a node as loaded
     */
    public markNodeLoaded(node: QuadtreeNode): void {
        node.state = NodeState.LOADED;
        this.loadingNodes.delete(node);
        
        // Remove from loading queue
        const index = this.loadingQueue.indexOf(node);
        if (index !== -1) {
            this.loadingQueue.splice(index, 1);
        }
        
        // Update memory usage
        this.memoryUsage += 0.05; // 50KB estimate per node
    }

    /**
     * Mark a node as failed to load
     */
    public markNodeFailed(node: QuadtreeNode): void {
        node.state = NodeState.FAILED;
        this.loadingNodes.delete(node);
        
        // Remove from loading queue
        const index = this.loadingQueue.indexOf(node);
        if (index !== -1) {
            this.loadingQueue.splice(index, 1);
        }
    }

    /**
     * Reset the LOD selector
     */
    public reset(): void {
        this.selectedNodes.clear();
        this.loadingNodes.clear();
        this.loadingQueue = [];
        this.frameNumber = 0;
        this.memoryUsage = 0;
        
        // Dispose all nodes
        for (const rootNode of this.rootNodes) {
            rootNode.dispose();
        }
        
        // Reinitialize
        this.initializeRootNodes();
    }
}
